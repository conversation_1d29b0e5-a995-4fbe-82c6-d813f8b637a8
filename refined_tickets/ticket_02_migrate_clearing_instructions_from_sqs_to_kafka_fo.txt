# Migrate Clearing Instructions from SQS to Kafka for Payment Processing

**Original Idea:** Clearing instructions are to be sent via Kafka and not SQS

*Business Value:*
Improves message reliability and real-time processing capabilities, ensuring compliance with financial transaction standards.

*Acceptance Criteria:*
* Kafka is configured and integrated with the payment processing module
* Clearing instructions are successfully sent via Kafka without data loss

*Open Questions:*
* What are the specific Kafka topics and brokers required for this migration?
* How will this affect existing transaction validation workflows?

*Technical Design:*
Replace SQS message sending logic in 'internal/auth/internal/model/financial_transaction.go' with Kafka producers. Update the event handling in 'internal/auth/internal/scheme/jcb/converters_events_test.go' to consume from Kafka. Ensure message serialization and deserialization are compatible with current data structures.

*Impact and Notifications:*
Low risk with moderate coordination required between the operations and compliance teams to validate message integrity.

*Likely Files to Modify:*

* internal/auth/internal/model/financial_transaction.go
* internal/auth/internal/scheme/jcb/converters_events_test.go

*Related Tickets:*

* MARS-1933: dashboards are not reacting to scheme dropdown change
* MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
* MARS-2622: MC transactions failed to be stored

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined