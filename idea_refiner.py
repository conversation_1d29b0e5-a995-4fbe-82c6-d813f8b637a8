#!/usr/bin/env python3
"""
LLM-Powered Idea Refiner for Payment Processing Systems

This script transforms high-level payment processing ideas into comprehensive,
domain-specific JIRA ticket descriptions using intelligent LLM prompting and
contextual knowledge injection.

Key Features:
- LLM-based approach with intelligent context management
- Payment processing domain expertise
- Smart similarity-based ticket retrieval
- Scheme-specific knowledge (Visa, MC, Amex, JCB)
- Architectural pattern recognition
- Actionable, specific acceptance criteria
- JSON-structured output with validation and retry logic
"""

import argparse
import json
import logging
import re
import sys
import math
import time
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import defaultdict, Counter
from functools import lru_cache

# Import MLX components for LLM integration
try:
    from mlx_lm import load, generate
    from mlx_lm.sample_utils import make_sampler, make_logits_processors
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    logging.warning("MLX not available - LLM-based refinement requires MLX")

# Model presets with context windows
PRESETS = {
    "gemma3": "/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
    "qwen3": "/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
    "alma13": "/Users/<USER>/.lmstudio/models/mlx-community/ALMA-13B-R-4bit-mlx",
}

# Context windows for each model
MAX_CONTEXT = {
    "gemma3": 8192,
    "qwen3": 32768,
    "alma13": 4096,
}


class LLMDebugLogger:
    """Comprehensive logging for LLM interactions to enable efficient debugging."""

    def __init__(self, debug_dir: Optional[Path] = None, enabled: bool = True):
        self.enabled = enabled
        self.logger = logging.getLogger(__name__)

        if not self.enabled:
            self.debug_dir = None
            return

        # Create timestamped debug directory
        if debug_dir is None:
            debug_dir = Path("llm_debug_logs")

        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.debug_dir = debug_dir / timestamp
        self.debug_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"Debug logging enabled: {self.debug_dir}")

        # Initialize metadata
        self.metadata = {
            "start_time": datetime.now().isoformat(),
            "script_version": "LLM-powered idea refiner v2.0",
            "ideas_processed": 0,
            "errors": [],
            "processing_times": {}
        }

    def log_run_start(self, args: argparse.Namespace, model_preset: str):
        """Log script run parameters and configuration."""
        if not self.enabled:
            return

        self.metadata.update({
            "model_preset": model_preset,
            "ideas_file": str(args.ideas),
            "jira_tickets_file": str(args.jira_tickets),
            "enriched_tickets_file": str(args.enriched_tickets),
            "tree_log_file": str(args.tree_log),
            "git_log_file": str(args.git_log),
            "output_dir": str(args.output_dir),
            "log_level": args.log_level
        })

    def log_idea_processing(self, idea_num: int, idea: str, context: Dict[str, Any],
                           prompt: str, response: str, sections: Dict[str, str],
                           refined: 'RefinedIdea', processing_time: float):
        """Log complete LLM interaction for a single idea."""
        if not self.enabled:
            return

        idea_prefix = f"idea_{idea_num:02d}"

        # 1. Save prompt
        prompt_file = self.debug_dir / f"{idea_prefix}_prompt.txt"
        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(f"=== PROMPT FOR IDEA {idea_num} ===\n")
            f.write(f"Original Idea: {idea}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Processing Time: {processing_time:.2f} seconds\n")
            f.write("=" * 50 + "\n\n")
            f.write(prompt)

        # 2. Save raw response
        response_file = self.debug_dir / f"{idea_prefix}_response.txt"
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(f"=== RAW LLM RESPONSE FOR IDEA {idea_num} ===\n")
            f.write(f"Original Idea: {idea}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Response Length: {len(response)} characters\n")
            f.write("=" * 50 + "\n\n")
            f.write(response)

        # 3. Save context (make it JSON serializable)
        context_file = self.debug_dir / f"{idea_prefix}_context.json"
        serializable_context = self._make_context_serializable(context)
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump({
                "idea_number": idea_num,
                "original_idea": idea,
                "timestamp": datetime.now().isoformat(),
                "context": serializable_context
            }, f, indent=2, ensure_ascii=False)

        # 4. Save parsed sections and final result
        parsed_file = self.debug_dir / f"{idea_prefix}_parsed.json"
        with open(parsed_file, 'w', encoding='utf-8') as f:
            json.dump({
                "idea_number": idea_num,
                "original_idea": idea,
                "timestamp": datetime.now().isoformat(),
                "extracted_sections": sections,
                "refined_idea": self._make_refined_idea_serializable(refined),
                "processing_time_seconds": processing_time
            }, f, indent=2, ensure_ascii=False)

        # Update metadata
        self.metadata["ideas_processed"] += 1
        self.metadata["processing_times"][f"idea_{idea_num}"] = processing_time

        self.logger.info(f"Debug logs saved for idea {idea_num}: {idea_prefix}_*.txt/json")

    def log_error(self, idea_num: int, idea: str, error: Exception):
        """Log processing errors."""
        if not self.enabled:
            return

        error_info = {
            "idea_number": idea_num,
            "idea": idea,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }

        self.metadata["errors"].append(error_info)

        # Save detailed error log
        error_file = self.debug_dir / f"idea_{idea_num:02d}_error.json"
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_info, f, indent=2, ensure_ascii=False)

    def finalize_run(self):
        """Save final metadata and close logging."""
        if not self.enabled:
            return

        self.metadata["end_time"] = datetime.now().isoformat()
        self.metadata["total_runtime_seconds"] = (
            datetime.fromisoformat(self.metadata["end_time"]) -
            datetime.fromisoformat(self.metadata["start_time"])
        ).total_seconds()

        metadata_file = self.debug_dir / "run_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Debug session complete. Logs saved to: {self.debug_dir}")
        self.logger.info(f"Processed {self.metadata['ideas_processed']} ideas with {len(self.metadata['errors'])} errors")

    def _make_context_serializable(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Convert context to JSON-serializable format."""
        serializable = {}

        for key, value in context.items():
            if key == 'similar_tickets':
                # Convert ContextualTicket objects to dicts
                serializable[key] = [
                    {
                        'ticket': ctx_ticket.ticket,
                        'similarity_score': ctx_ticket.similarity_score,
                        'file_changes': ctx_ticket.file_changes
                    }
                    for ctx_ticket in value
                ]
            else:
                serializable[key] = value

        return serializable

    def _make_refined_idea_serializable(self, refined: 'RefinedIdea') -> Dict[str, Any]:
        """Convert RefinedIdea to JSON-serializable format."""
        return {
            "original_idea": refined.original_idea,
            "title": refined.title,
            "business_value": refined.business_value,
            "description": refined.description,
            "acceptance_criteria": refined.acceptance_criteria,
            "open_questions": refined.open_questions,
            "suggested_design": refined.suggested_design,
            "likely_files": refined.likely_files,
            "impact_assessment": refined.impact_assessment,
            "related_tickets": refined.related_tickets,
            "context_used_keys": list(refined.context_used.keys()) if refined.context_used else []
        }


@dataclass
class ProjectKnowledge:
    """Container for all project knowledge extracted from input files."""
    tickets: List[Dict]
    enriched_tickets: List[Dict]
    project_structure: List[str]
    git_commits: List[str]

    # Derived knowledge for LLM context
    scheme_patterns: Dict[str, List[Dict]]  # Visa, MC, Amex, JCB specific tickets
    file_change_patterns: Dict[str, List[str]]  # Common file patterns by domain
    architectural_components: Set[str]  # Key system components
    domain_examples: Dict[str, List[Dict]]  # Example tickets by domain


@dataclass
class ContextualTicket:
    """A ticket with similarity score for context ranking."""
    ticket: Dict
    similarity_score: float
    file_changes: List[str]


@dataclass
class RefinedIdea:
    """Container for a refined idea with LLM-generated content."""
    original_idea: str
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    related_tickets: List[str]
    context_used: Dict[str, Any]  # Track what context was provided to LLM


class SmartKnowledgeExtractor:
    """Extracts and organizes knowledge for intelligent LLM context injection."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def extract_knowledge(self, jira_path: Path, enriched_path: Path,
                         tree_path: Path, git_path: Path) -> ProjectKnowledge:
        """Extract and organize knowledge for LLM context."""
        self.logger.info("Extracting payment processing domain knowledge...")

        # Load raw data
        tickets = self._load_json(jira_path)
        enriched_tickets = self._load_json(enriched_path)
        project_structure = self._load_text_lines(tree_path)
        git_commits = self._load_text_lines(git_path)

        # Organize by payment schemes and domains
        scheme_patterns = self._extract_scheme_patterns(tickets)
        file_change_patterns = self._extract_file_patterns(enriched_tickets)
        architectural_components = self._extract_architectural_components(project_structure)
        domain_examples = self._extract_domain_examples(tickets, enriched_tickets)

        self.logger.info(f"Organized {len(tickets)} tickets into domain-specific patterns")
        self.logger.info(f"Found {len(scheme_patterns)} scheme-specific patterns")

        return ProjectKnowledge(
            tickets=tickets,
            enriched_tickets=enriched_tickets,
            project_structure=project_structure,
            git_commits=git_commits,
            scheme_patterns=scheme_patterns,
            file_change_patterns=file_change_patterns,
            architectural_components=architectural_components,
            domain_examples=domain_examples
        )
    
    def _load_json(self, path: Path) -> List[Dict]:
        """Load JSON file safely."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []

    def _load_text_lines(self, path: Path) -> List[str]:
        """Load text file as lines."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []

    def _extract_scheme_patterns(self, tickets: List[Dict]) -> Dict[str, List[Dict]]:
        """Extract tickets by payment scheme for scheme-specific context."""
        schemes = {
            'visa': ['visa'],
            'mastercard': ['mc', 'mastercard'],
            'amex': ['amex', 'american express'],
            'jcb': ['jcb'],
            'diners': ['diners'],
            'unionpay': ['unionpay']
        }

        scheme_tickets = defaultdict(list)

        for ticket in tickets:
            content = f"{ticket.get('title', '')} {ticket.get('description', '')}".lower()
            for scheme, keywords in schemes.items():
                if any(keyword in content for keyword in keywords):
                    scheme_tickets[scheme].append(ticket)

        return dict(scheme_tickets)

    def _extract_domain_examples(self, tickets: List[Dict], enriched_tickets: List[Dict]) -> Dict[str, List[Dict]]:
        """Extract example tickets by domain for context."""
        domains = {
            'authorization': ['auth', 'authorization', 'approve', 'decline'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'reversal': ['reversal', 'void', 'cancel'],
            'migration': ['migration', 'migrate', 'database', 'schema'],
            'messaging': ['kafka', 'sqs', 'queue', 'message'],
            'validation': ['validation', 'validate', 'block', 'reject'],
            'datetime': ['datetime', 'timestamp', 'time', 'timezone']
        }

        domain_tickets = defaultdict(list)

        # Create enriched lookup
        enriched_lookup = {t['key']: t for t in enriched_tickets}

        for ticket in tickets:
            content = f"{ticket.get('title', '')} {ticket.get('description', '')}".lower()
            for domain, keywords in domains.items():
                if any(keyword in content for keyword in keywords):
                    # Add file changes if available
                    enriched = enriched_lookup.get(ticket.get('key'))
                    if enriched and enriched.get('files_changed'):
                        ticket_with_files = ticket.copy()
                        ticket_with_files['files_changed'] = enriched['files_changed']
                        domain_tickets[domain].append(ticket_with_files)
                    else:
                        domain_tickets[domain].append(ticket)

        return dict(domain_tickets)

    def _extract_file_patterns(self, enriched_tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract file change patterns by domain."""
        patterns = defaultdict(list)

        for ticket in enriched_tickets:
            title = ticket.get('title', '').lower()
            files_changed = ticket.get('files_changed', [])

            if not files_changed:
                continue

            file_paths = [f['file_path'] for f in files_changed]

            # Categorize by content
            if any(word in title for word in ['auth', 'authorization']):
                patterns['auth'].extend(file_paths)
            elif any(word in title for word in ['clearing', 'settlement']):
                patterns['clearing'].extend(file_paths)
            elif any(word in title for word in ['reversal', 'void']):
                patterns['reversal'].extend(file_paths)
            elif any(word in title for word in ['migration', 'database']):
                patterns['migration'].extend(file_paths)
            elif any(word in title for word in ['kafka', 'sqs', 'message']):
                patterns['messaging'].extend(file_paths)

        # Remove duplicates and get most common
        for domain in patterns:
            file_counter = Counter(patterns[domain])
            patterns[domain] = [file for file, count in file_counter.most_common(10)]

        return dict(patterns)

    def _extract_architectural_components(self, project_structure: List[str]) -> Set[str]:
        """Extract key architectural components from project structure."""
        components = set()

        for line in project_structure:
            if 'internal/auth/' in line:
                components.add('auth_services')
            if 'internal/clearing/' in line:
                components.add('clearing_services')
            if 'internal/scheme/' in line:
                components.add('scheme_handlers')
            if 'componenttest/' in line:
                components.add('component_tests')
            if 'helm/' in line:
                components.add('kubernetes_deployment')
            if 'migrations/' in line:
                components.add('database_migrations')
            if 'kafka' in line.lower():
                components.add('kafka_messaging')
            if 'sqs' in line.lower():
                components.add('sqs_messaging')

        return components


class IntelligentLLMRefiner:
    """LLM-powered refiner with intelligent context injection and domain expertise."""

    def __init__(self, model_preset: str = "qwen3", debug_logger: Optional[LLMDebugLogger] = None):
        self.logger = logging.getLogger(__name__)
        self.model_preset = model_preset
        self.max_context = MAX_CONTEXT.get(model_preset, 4096)
        self.debug_logger = debug_logger

        if not MLX_AVAILABLE:
            self.logger.error("MLX not available - LLM refinement requires MLX")
            raise RuntimeError("MLX required for LLM-based refinement")

        try:
            model_path = PRESETS[model_preset]
            self.logger.info(f"Loading {model_preset} model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.logger.info(f"Model loaded successfully (context: {self.max_context} tokens)")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    @lru_cache(maxsize=1000)
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using model tokenizer."""
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            return len(text) // 4  # Rough estimate

    def refine_idea(self, idea: str, knowledge: ProjectKnowledge, idea_num: int = 0) -> RefinedIdea:
        """Refine idea using LLM with JSON output, validation, and retry logic."""
        start_time = time.time()
        self.logger.info(f"Refining idea {idea_num} with LLM (JSON mode): {idea[:60]}...")

        max_retries = 2
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # Build contextual prompt with relevant knowledge
                context = self._build_intelligent_context(idea, knowledge)
                prompt = self._create_domain_aware_prompt(idea, context)

                # Generate response
                response = self._generate_llm_response(prompt)

                # Parse JSON response with validation
                refined = self._parse_llm_response(idea, response, context)

                processing_time = time.time() - start_time

                # For debug logging, we need sections for compatibility
                sections = {"json_response": response}

                # Log debug information
                if self.debug_logger:
                    self.debug_logger.log_idea_processing(
                        idea_num, idea, context, prompt, response, sections, refined, processing_time
                    )

                self.logger.info(f"Generated refined ticket {idea_num}: {refined.title} ({processing_time:.2f}s)")
                if attempt > 0:
                    self.logger.info(f"Success on retry attempt {attempt}")

                return refined

            except json.JSONDecodeError as e:
                last_error = e
                self.logger.warning(f"JSON parsing failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries:
                    self.logger.info(f"Retrying with more explicit JSON prompt...")
                    # Could modify prompt here for retry
                    continue
                else:
                    self.logger.error(f"All JSON parsing attempts failed for idea {idea_num}")

            except Exception as e:
                last_error = e
                self.logger.error(f"Failed to refine idea {idea_num} on attempt {attempt + 1}: {e}")
                if attempt < max_retries:
                    continue
                else:
                    break

        # If we get here, all attempts failed
        processing_time = time.time() - start_time

        # Log error
        if self.debug_logger:
            self.debug_logger.log_error(idea_num, idea, last_error)

        raise RuntimeError(f"Failed to refine idea {idea_num} after {max_retries + 1} attempts: {last_error}")

    def _build_intelligent_context(self, idea: str, knowledge: ProjectKnowledge) -> Dict[str, Any]:
        """Build intelligent context based on idea similarity and relevance."""
        context = {}

        # Find most relevant tickets by similarity
        relevant_tickets = self._find_similar_tickets(idea, knowledge.tickets, knowledge.enriched_tickets)
        context['similar_tickets'] = relevant_tickets[:3]  # Top 3 most similar

        # Get domain-specific examples
        domain = self._identify_primary_domain(idea)
        if domain in knowledge.domain_examples:
            context['domain_examples'] = knowledge.domain_examples[domain][:2]

        # Get scheme-specific context if applicable
        scheme = self._identify_scheme(idea)
        if scheme and scheme in knowledge.scheme_patterns:
            context['scheme_examples'] = knowledge.scheme_patterns[scheme][:2]

        # Get relevant file patterns
        if domain in knowledge.file_change_patterns:
            context['file_patterns'] = knowledge.file_change_patterns[domain]

        # Add architectural context
        context['components'] = list(knowledge.architectural_components)

        # Estimate context size and trim if needed
        context = self._optimize_context_size(context)

        return context

    def _find_similar_tickets(self, idea: str, tickets: List[Dict], enriched_tickets: List[Dict]) -> List[ContextualTicket]:
        """Find tickets most similar to the idea using keyword overlap."""
        idea_words = set(idea.lower().split())
        enriched_lookup = {t['key']: t for t in enriched_tickets}

        scored_tickets = []

        for ticket in tickets:
            title = ticket.get('title', '').lower()
            desc = ticket.get('description', '').lower()
            content_words = set(f"{title} {desc}".split())

            # Calculate similarity score
            overlap = len(idea_words.intersection(content_words))
            total_words = len(idea_words.union(content_words))
            similarity = overlap / total_words if total_words > 0 else 0

            if similarity > 0.1:  # Minimum threshold
                enriched = enriched_lookup.get(ticket.get('key'))
                file_changes = []
                if enriched and enriched.get('files_changed'):
                    file_changes = [f['file_path'] for f in enriched['files_changed']]

                scored_tickets.append(ContextualTicket(
                    ticket=ticket,
                    similarity_score=similarity,
                    file_changes=file_changes
                ))

        # Sort by similarity score
        scored_tickets.sort(key=lambda x: x.similarity_score, reverse=True)
        return scored_tickets

    def _identify_primary_domain(self, idea: str) -> str:
        """Identify the primary domain of the idea."""
        idea_lower = idea.lower()

        domain_keywords = {
            'authorization': ['auth', 'authorization', 'approve', 'decline', 'block'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'reversal': ['reversal', 'void', 'cancel'],
            'migration': ['migration', 'migrate', 'database', 'schema', 'squash'],
            'messaging': ['kafka', 'sqs', 'queue', 'message'],
            'validation': ['validation', 'validate', 'block', 'reject'],
            'datetime': ['datetime', 'timestamp', 'time', 'timezone', 'local']
        }

        for domain, keywords in domain_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                return domain

        return 'general'

    def _identify_scheme(self, idea: str) -> Optional[str]:
        """Identify if idea is specific to a payment scheme."""
        idea_lower = idea.lower()

        if any(word in idea_lower for word in ['mc', 'mastercard']):
            return 'mastercard'
        elif 'visa' in idea_lower:
            return 'visa'
        elif 'amex' in idea_lower:
            return 'amex'
        elif 'jcb' in idea_lower:
            return 'jcb'
        elif 'diners' in idea_lower:
            return 'diners'

        return None

    def _optimize_context_size(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize context size to fit within model limits."""
        # Reserve tokens for prompt template and response
        available_tokens = self.max_context - 2000  # Reserve for prompt + response

        # Convert context to serializable format for size estimation
        serializable_context = self._make_context_serializable(context)
        context_text = json.dumps(serializable_context, indent=2)
        current_tokens = self._count_tokens(context_text)

        if current_tokens <= available_tokens:
            return context

        # Trim context intelligently
        optimized = context.copy()

        # Reduce similar tickets if needed
        if 'similar_tickets' in optimized and len(optimized['similar_tickets']) > 1:
            optimized['similar_tickets'] = optimized['similar_tickets'][:1]

        # Reduce domain examples
        if 'domain_examples' in optimized and len(optimized['domain_examples']) > 1:
            optimized['domain_examples'] = optimized['domain_examples'][:1]

        # Limit file patterns
        if 'file_patterns' in optimized and len(optimized['file_patterns']) > 5:
            optimized['file_patterns'] = optimized['file_patterns'][:5]

        return optimized

    def _make_context_serializable(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Convert context to JSON-serializable format."""
        serializable = {}

        for key, value in context.items():
            if key == 'similar_tickets':
                # Convert ContextualTicket objects to dicts
                serializable[key] = [
                    {
                        'ticket': ctx_ticket.ticket,
                        'similarity_score': ctx_ticket.similarity_score,
                        'file_changes': ctx_ticket.file_changes
                    }
                    for ctx_ticket in value
                ]
            else:
                serializable[key] = value

        return serializable

    def _create_domain_aware_prompt(self, idea: str, context: Dict[str, Any]) -> str:
        """Create improved JSON-focused prompt for structured output."""

        # Build context sections more concisely
        context_sections = []

        # Add similar tickets for context
        if context.get('similar_tickets'):
            context_sections.append("SIMILAR TICKETS:")
            for i, ctx_ticket in enumerate(context['similar_tickets'][:2], 1):  # Limit to 2
                ticket = ctx_ticket.ticket
                context_sections.append(f"{i}. {ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")

        # Add file patterns
        if context.get('file_patterns'):
            context_sections.append(f"COMMON FILES: {', '.join(context['file_patterns'][:3])}")

        context_text = "\n".join(context_sections) if context_sections else "No context available."

        prompt = f"""Transform this payment processing idea into a JIRA ticket. Return only valid JSON.

CONTEXT: {context_text}

IDEA: "{idea}"

Required JSON format:
{{
  "title": "Specific actionable title",
  "business_value": "Clear business impact and compliance benefits",
  "acceptance_criteria": ["Specific testable criteria", "Another specific criteria"],
  "open_questions": ["Specific technical question", "Specific business question"],
  "technical_design": "Detailed implementation approach with components and steps",
  "likely_files": ["path/to/file1.java", "path/to/file2.yaml"],
  "impact_assessment": "Risk level and coordination requirements"
}}

JSON:"""

        return prompt

    def _generate_llm_response(self, prompt: str) -> str:
        """Generate response using LLM with improved parameters to prevent loops."""
        try:
            # Check prompt size
            prompt_tokens = self._count_tokens(prompt)
            max_response_tokens = min(1024, self.max_context - prompt_tokens - 100)  # Reduced max tokens

            if max_response_tokens < 300:
                self.logger.warning(f"Limited response tokens: {max_response_tokens}")

            self.logger.debug(f"Prompt tokens: {prompt_tokens}, Max response: {max_response_tokens}")

            # Improved sampling parameters to prevent repetitive loops
            sampler = make_sampler(
                temp=0.7,        # Higher temperature for more creativity
                top_p=0.95       # Slightly higher top_p
            )

            # Add repetition penalty using logits processors
            logits_processors = make_logits_processors(
                repetition_penalty=1.1,
                repetition_context_size=20
            )

            response = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=max_response_tokens,
                sampler=sampler,
                logits_processors=logits_processors,
                verbose=False
            )

            # Log response for debugging
            self.logger.debug(f"LLM Response: {response[:200]}...")

            # Check for repetitive content and truncate if needed
            response = self._clean_repetitive_response(response)

            return response.strip()

        except Exception as e:
            self.logger.error(f"LLM generation failed: {e}")
            raise

    def _clean_repetitive_response(self, response: str) -> str:
        """Clean repetitive content from LLM response."""
        # First, try to find the first complete JSON object
        import re

        # Look for JSON pattern
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response)

        if matches:
            # Return the first complete JSON match
            return matches[0]

        # Fallback: line-based repetition cleaning
        lines = response.split('\n')
        cleaned_lines = []
        seen_lines = set()
        repetition_count = 0

        for line in lines:
            line_clean = line.strip()
            if line_clean in seen_lines:
                repetition_count += 1
                if repetition_count > 2:  # Stop after 2 repetitions
                    break
            else:
                repetition_count = 0
                seen_lines.add(line_clean)

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _parse_llm_response(self, idea: str, response: str, context: Dict[str, Any]) -> RefinedIdea:
        """Parse JSON LLM response into structured RefinedIdea with validation and fallback."""

        # Try to parse JSON response
        try:
            parsed_json = self._extract_and_validate_json(response)

            # Extract data from JSON
            title = parsed_json.get('title', idea.strip())
            business_value = parsed_json.get('business_value', 'Business value to be determined')
            acceptance_criteria = parsed_json.get('acceptance_criteria', [])
            open_questions = parsed_json.get('open_questions', [])
            technical_design = parsed_json.get('technical_design', 'Technical design to be determined')
            likely_files = parsed_json.get('likely_files', [])
            impact_assessment = parsed_json.get('impact_assessment', 'Impact assessment to be determined')

            # Validate lists are actually lists
            if not isinstance(acceptance_criteria, list):
                acceptance_criteria = [str(acceptance_criteria)] if acceptance_criteria else []
            if not isinstance(open_questions, list):
                open_questions = [str(open_questions)] if open_questions else []
            if not isinstance(likely_files, list):
                likely_files = [str(likely_files)] if likely_files else []

        except Exception as e:
            self.logger.warning(f"JSON parsing failed: {e}. Attempting fallback parsing.")
            # Fallback to text parsing if JSON fails
            sections = self._extract_sections(response)
            title = sections.get('TITLE', idea.strip())
            business_value = sections.get('BUSINESS_VALUE', 'Business value to be determined')
            acceptance_criteria = self._parse_list_section(sections.get('ACCEPTANCE_CRITERIA', ''))
            open_questions = self._parse_list_section(sections.get('OPEN_QUESTIONS', ''))
            likely_files = self._parse_list_section(sections.get('LIKELY_FILES', ''))
            technical_design = sections.get('TECHNICAL_DESIGN', 'Technical design to be determined')
            impact_assessment = sections.get('IMPACT_ASSESSMENT', 'Impact assessment to be determined')

        # Build description
        description = self._build_jira_description(
            business_value, acceptance_criteria, open_questions,
            technical_design, impact_assessment
        )

        # Extract related tickets from context
        related_tickets = []
        if context.get('similar_tickets'):
            for ctx_ticket in context['similar_tickets']:
                ticket = ctx_ticket.ticket
                related_tickets.append(f"{ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")

        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=technical_design,
            likely_files=likely_files,
            impact_assessment=impact_assessment,
            related_tickets=related_tickets,
            context_used=context
        )

    def _extract_and_validate_json(self, response: str) -> Dict[str, Any]:
        """Extract and validate JSON from LLM response with improved parsing."""
        # Clean the response more aggressively
        cleaned = response.strip()

        # Remove common LLM artifacts
        cleaned = re.sub(r'^.*?(?=\{)', '', cleaned, flags=re.DOTALL)  # Remove everything before first {
        cleaned = re.sub(r'\}.*?$', '}', cleaned, flags=re.DOTALL)     # Remove everything after last }

        # Find JSON boundaries more carefully
        brace_count = 0
        start_idx = -1
        end_idx = -1

        for i, char in enumerate(cleaned):
            if char == '{':
                if start_idx == -1:
                    start_idx = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0 and start_idx != -1:
                    end_idx = i
                    break

        if start_idx == -1 or end_idx == -1:
            raise ValueError("No complete JSON object found in response")

        json_str = cleaned[start_idx:end_idx + 1]

        # Parse JSON with multiple attempts
        for attempt in range(3):
            try:
                parsed = json.loads(json_str)
                break
            except json.JSONDecodeError as e:
                if attempt < 2:
                    # Try to fix common JSON issues
                    json_str = self._fix_common_json_issues(json_str)
                else:
                    raise e

        # Validate and fix required fields
        required_fields = {
            'title': 'Refined ticket title',
            'business_value': 'Business value and impact',
            'acceptance_criteria': [],
            'open_questions': [],
            'technical_design': 'Technical implementation approach',
            'likely_files': [],
            'impact_assessment': 'Impact and risk assessment'
        }

        for field, default_value in required_fields.items():
            if field not in parsed:
                self.logger.warning(f"Missing required field: {field}")
                parsed[field] = default_value
            elif not parsed[field]:  # Handle empty values
                parsed[field] = default_value

        return parsed

    def _fix_common_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues from LLM responses."""
        # Fix trailing commas
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        # Fix unescaped quotes in strings (more careful approach)
        json_str = re.sub(r'(?<!\\)"(?=[^"]*"[^"]*:)', r'\\"', json_str)

        # Fix single quotes to double quotes for keys
        json_str = re.sub(r"'([^']*)'(\s*):", r'"\1"\2:', json_str)

        # Fix missing quotes around keys
        json_str = re.sub(r'(\w+)(\s*):', r'"\1"\2:', json_str)

        # Fix newlines in strings
        json_str = re.sub(r'"\s*\n\s*"', r'" "', json_str)

        # Remove any remaining control characters
        json_str = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_str)

        return json_str

    def _extract_sections(self, response: str) -> Dict[str, str]:
        """Extract sections from LLM response, handling various formats and cleaning up."""
        sections = {}

        # Clean response - remove model artifacts and reasoning
        cleaned_response = self._clean_llm_response(response)

        # Find the structured part of the response (after any preamble)
        lines = cleaned_response.split('\n')
        start_idx = 0

        # Look for the first section header
        section_headers = ['TITLE:', 'BUSINESS_VALUE:', 'ACCEPTANCE_CRITERIA:',
                          'OPEN_QUESTIONS:', 'TECHNICAL_DESIGN:', 'LIKELY_FILES:',
                          'IMPACT_ASSESSMENT:']

        for i, line in enumerate(lines):
            if any(line.strip().startswith(header) for header in section_headers):
                start_idx = i
                break

        # Parse from the structured part
        current_section = None
        current_content = []

        for line in lines[start_idx:]:
            line = line.strip()

            # Skip empty lines at start of sections
            if not line and not current_content:
                continue

            # Stop if we hit model reasoning or artifacts
            if any(stop_phrase in line.lower() for stop_phrase in [
                'okay, i need to', 'let me start', 'first, the title',
                'business value:', 'for acceptance criteria', 'i think that',
                'make sure', 'i need to make sure', 'check if'
            ]):
                break

            header_found = None
            for header in section_headers:
                if line.startswith(header):
                    header_found = header.rstrip(':')
                    break

            if header_found:
                # Save previous section
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()

                # Start new section
                current_section = header_found
                current_content = []

                # Check if content is on same line
                content_after_header = line[len(header_found) + 1:].strip()
                if content_after_header:
                    current_content.append(content_after_header)
            else:
                if current_section and line:
                    current_content.append(line)

        # Save final section
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def _clean_llm_response(self, response: str) -> str:
        """Clean LLM response by removing artifacts and reasoning."""
        # Remove common LLM artifacts
        cleaned = response

        # Remove end-of-text tokens
        cleaned = re.sub(r'<\|endoftext\|>.*$', '', cleaned, flags=re.DOTALL)

        # Remove "Human:" and subsequent content
        cleaned = re.sub(r'Human:.*$', '', cleaned, flags=re.DOTALL)

        # Remove model reasoning patterns
        reasoning_patterns = [
            r'Okay, I need to.*?(?=TITLE:|$)',
            r'Let me start.*?(?=TITLE:|$)',
            r'First, the title.*?(?=TITLE:|$)',
            r'I need to make sure.*?(?=TITLE:|$)',
            r'no fluff, no generic statements.*?(?=TITLE:|$)'
        ]

        for pattern in reasoning_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.DOTALL | re.IGNORECASE)

        return cleaned.strip()

    def _parse_list_section(self, content: str) -> List[str]:
        """Parse list section into individual items."""
        if not content:
            return []

        items = []
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('- '):
                items.append(line[2:].strip())
            elif line.startswith('* '):
                items.append(line[2:].strip())
            elif line and not line.startswith(' '):
                items.append(line)

        return [item for item in items if item]

    def _build_jira_description(self, business_value: str, acceptance_criteria: List[str],
                               open_questions: List[str], technical_design: str,
                               impact_assessment: str) -> str:
        """Build JIRA-formatted description."""
        parts = [
            f"*Business Value:*\n{business_value}\n",
            "*Acceptance Criteria:*"
        ]

        for criteria in acceptance_criteria:
            parts.append(f"* {criteria}")

        parts.extend(["\n*Open Questions:*"])
        for question in open_questions:
            parts.append(f"* {question}")

        parts.extend([
            f"\n*Technical Design:*\n{technical_design}\n",
            f"*Impact and Notifications:*\n{impact_assessment}"
        ])

        return "\n".join(parts)

class LLMIdeaRefinerApp:
    """Main application class using LLM-powered refinement with debug logging."""

    def __init__(self, model_preset: str = "qwen3", debug_dir: Optional[Path] = None,
                 debug_enabled: bool = True):
        self.logger = logging.getLogger(__name__)
        self.knowledge_extractor = SmartKnowledgeExtractor()

        if not MLX_AVAILABLE:
            self.logger.error("This version requires MLX for LLM-based refinement")
            raise RuntimeError("MLX required - install with: pip install mlx-lm")

        # Initialize debug logging
        self.debug_logger = LLMDebugLogger(debug_dir, debug_enabled)

        # Initialize LLM refiner with debug logger
        self.llm_refiner = IntelligentLLMRefiner(model_preset, self.debug_logger)

    def refine_ideas_from_file(self, ideas_path: Path, jira_path: Path,
                              enriched_path: Path, tree_path: Path,
                              git_path: Path, output_dir: Path, args: argparse.Namespace) -> None:
        """Refine all ideas from a file and generate ticket descriptions with debug logging."""

        # Log run start
        self.debug_logger.log_run_start(args, self.llm_refiner.model_preset)

        try:
            # Extract project knowledge
            knowledge = self.knowledge_extractor.extract_knowledge(
                jira_path, enriched_path, tree_path, git_path
            )

            # Load ideas
            ideas = self._load_ideas(ideas_path)
            self.logger.info(f"Loaded {len(ideas)} ideas to refine")

            # Create output directory
            output_dir.mkdir(parents=True, exist_ok=True)

            # Process each idea
            for i, idea in enumerate(ideas, 1):
                self.logger.info(f"Processing idea {i}/{len(ideas)}: {idea[:50]}...")

                try:
                    refined = self.llm_refiner.refine_idea(idea, knowledge, idea_num=i)

                    # Generate output filename
                    safe_title = self._make_safe_filename(refined.title)
                    output_file = output_dir / f"ticket_{i:02d}_{safe_title}.txt"

                    # Write ticket description
                    self._write_ticket_file(refined, output_file)

                    self.logger.info(f"Generated ticket: {output_file}")

                except Exception as e:
                    self.logger.error(f"Failed to process idea {i}: {e}")
                    continue

            self.logger.info(f"Completed processing {len(ideas)} ideas")

        finally:
            # Finalize debug logging
            self.debug_logger.finalize_run()

    def _load_ideas(self, ideas_path: Path) -> List[str]:
        """Load ideas from text file."""
        try:
            with open(ideas_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]

            # Handle numbered lists
            ideas = []
            for line in lines:
                # Remove leading numbers and dots
                clean_line = re.sub(r'^\d+\.\s*', '', line)
                if clean_line:
                    ideas.append(clean_line)

            return ideas

        except Exception as e:
            self.logger.error(f"Failed to load ideas from {ideas_path}: {e}")
            return []

    def _make_safe_filename(self, title: str) -> str:
        """Convert title to safe filename."""
        # Remove/replace unsafe characters
        safe = re.sub(r'[^\w\s-]', '', title)
        safe = re.sub(r'[-\s]+', '_', safe)
        return safe.lower()[:50]  # Limit length

    def _write_ticket_file(self, refined: RefinedIdea, output_file: Path) -> None:
        """Write refined idea to ticket file."""
        content_parts = [
            f"# {refined.title}",
            "",
            f"**Original Idea:** {refined.original_idea}",
            "",
            refined.description,
            ""
        ]

        if refined.likely_files:
            content_parts.extend([
                "*Likely Files to Modify:*",
                ""
            ])
            for file_path in refined.likely_files:
                content_parts.append(f"* {file_path}")
            content_parts.append("")

        if refined.related_tickets:
            content_parts.extend([
                "*Related Tickets:*",
                ""
            ])
            for ticket in refined.related_tickets:
                content_parts.append(f"* {ticket}")
            content_parts.append("")

        content_parts.extend([
            "*Refined By:* Idea Refiner Script",
            "",
            f"*Dependencies:* To be determined",
            "",
            f"*Out of Scope:* To be determined"
        ])

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(content_parts))


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Refine high-level ideas into comprehensive JIRA ticket descriptions",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--ideas",
        type=Path,
        required=True,
        help="Text file containing ideas (one per line or numbered list)"
    )

    parser.add_argument(
        "--jira_tickets",
        type=Path,
        required=True,
        help="JSON file with historical JIRA tickets"
    )

    parser.add_argument(
        "--enriched_tickets",
        type=Path,
        required=True,
        help="JSON file with enriched JIRA tickets (with file changes)"
    )

    parser.add_argument(
        "--tree_log",
        type=Path,
        required=True,
        help="Text file with project structure (tree output)"
    )

    parser.add_argument(
        "--git_log",
        type=Path,
        required=True,
        help="Text file with git commit history"
    )

    parser.add_argument(
        "--output_dir",
        type=Path,
        default=Path("refined_tickets"),
        help="Directory to write refined ticket files"
    )

    parser.add_argument(
        "--model",
        choices=list(PRESETS.keys()),
        default="qwen3",
        help="LLM model preset to use for refinement"
    )

    parser.add_argument(
        "--log_level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    parser.add_argument(
        "--debug_dir",
        type=Path,
        default=Path("llm_debug_logs"),
        help="Directory for debug logs (creates timestamped subdirectories)"
    )

    parser.add_argument(
        "--no_debug_logs",
        action="store_true",
        help="Disable debug logging to save disk space"
    )

    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()

    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    logger = logging.getLogger(__name__)

    # Validate input files
    for file_path in [args.ideas, args.jira_tickets, args.enriched_tickets,
                      args.tree_log, args.git_log]:
        if not file_path.exists():
            logger.error(f"Input file not found: {file_path}")
            sys.exit(1)

    # Check MLX availability - required for LLM-based refinement
    if not MLX_AVAILABLE:
        logger.error("MLX not available - LLM-based refinement requires MLX")
        logger.error("Install MLX with: pip install mlx-lm")
        sys.exit(1)

    try:
        # Create and run the LLM-powered refiner with debug logging
        debug_enabled = not args.no_debug_logs
        app = LLMIdeaRefinerApp(
            model_preset=args.model,
            debug_dir=args.debug_dir,
            debug_enabled=debug_enabled
        )

        app.refine_ideas_from_file(
            args.ideas, args.jira_tickets, args.enriched_tickets,
            args.tree_log, args.git_log, args.output_dir, args
        )

        logger.info(f"LLM-powered refinement complete. Check output in: {args.output_dir}")
        if debug_enabled:
            logger.info(f"Debug logs available in: {args.debug_dir}")

    except Exception as e:
        logger.error(f"Refinement failed: {e}")
        if "MLX" in str(e):
            logger.error("Install MLX with: pip install mlx-lm")
        sys.exit(1)


if __name__ == "__main__":
    main()
