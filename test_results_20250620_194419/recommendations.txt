IDEA REFINER IMPROVEMENT RECOMMENDATIONS
==================================================

1. PROMPT IMPROVEMENT: Enhance prompt to request more specific, measurable language. Add examples of specific vs generic statements.

2. ACCEPTANCE CRITERIA: Improve prompt to generate more actionable acceptance criteria. Request 'Given-When-Then' format and specific validation steps.

3. TECHNICAL DETAIL: Enhance context injection to include more specific file paths, component names, and technical implementation details.

4. DOMAIN KNOWLEDGE: Improve payment processing domain context in prompts. Add more scheme-specific terminology and industry standards.

