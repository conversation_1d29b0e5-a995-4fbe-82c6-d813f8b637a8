# Align Amex reversal local datetime with terminal time

**Original Idea:** Amex wants a reversal local datetime to be exactly like the time on a terminal

*Business Value:*
Ensures compliance with Amex's data consistency requirements and prevents discrepancies in transaction records

*Acceptance Criteria:*
* Reversal requests must use the same local datetime as the terminal's clock
* DateTime format in reversal responses matches terminal's timezone and locale settings

*Open Questions:*
* How to synchronize the terminal's datetime with the backend service?
* What are Amex's exact timezone and locale standards for datetime formatting?

*Technical Design:*
1. Modify the reversal request processing pipeline to capture terminal's local datetime
2. Implement a time synchronization service between terminal and backend
3. Update datetime formatting module to adhere to Amex's timezone standards
4. Add validation layer to compare terminal and backend datetime values

*Impact and Notifications:*
Low risk. Requires coordination with Amex's compliance and terminal teams for timezone standards alignment

*Likely Files to Modify:*

* internal/auth/internal/scheme/mc/converters_reversal_requests.go
* componenttest/amexapi/request_v2.go

*Related Tickets:*

* MARS-2745: use correct reversal reason values for MC
* MARS-2400: AMEX tech debt
* MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined