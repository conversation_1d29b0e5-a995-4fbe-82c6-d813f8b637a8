{"individual_scores": [{"idea_number": 1, "original_idea": "I need to block any recurring auths for MC and JCB", "refined_title": "I need to block any recurring auths for MC and JCB", "metrics": {"clarity_score": 0, "actionability_score": 2.0, "technical_detail_score": 3.0, "domain_context_score": 2.0, "generic_content_penalty": 2.0, "overall_score": 1.25}, "issues": ["Contains generic 'to be determined' statements", "Low clarity score - needs more specific language", "Low actionability score - acceptance criteria not specific enough", "Low technical detail score - needs more concrete implementation details", "Low domain context score - needs more payment processing terminology"]}, {"idea_number": 2, "original_idea": "Clearing instructions are to be sent via Kafka and not SQS", "refined_title": "Clearing instructions are to be sent via Kafka and not SQS", "metrics": {"clarity_score": 2.0, "actionability_score": 2.0, "technical_detail_score": 3.0, "domain_context_score": 2.0, "generic_content_penalty": 2.0, "overall_score": 1.75}, "issues": ["Contains generic 'to be determined' statements", "Low clarity score - needs more specific language", "Low actionability score - acceptance criteria not specific enough", "Low technical detail score - needs more concrete implementation details", "Low domain context score - needs more payment processing terminology"]}, {"idea_number": 3, "original_idea": "Amex wants a reversal local datetime to be exactly like the time on a terminal", "refined_title": "Amex wants a reversal local datetime to be exactly like the time on a terminal", "metrics": {"clarity_score": 4.0, "actionability_score": 2.0, "technical_detail_score": 1.5, "domain_context_score": 2.0, "generic_content_penalty": 2.0, "overall_score": 1.875}, "issues": ["Contains generic 'to be determined' statements", "Low clarity score - needs more specific language", "Low actionability score - acceptance criteria not specific enough", "Low technical detail score - needs more concrete implementation details", "Low domain context score - needs more payment processing terminology"]}, {"idea_number": 4, "original_idea": "Squash all our DB migrations into a simpler big one", "refined_title": "Consolidate DB migrations into a single unified migration script", "metrics": {"clarity_score": 10.0, "actionability_score": 2.0, "technical_detail_score": 9.0, "domain_context_score": 1.0, "generic_content_penalty": 2.0, "overall_score": 5.0}, "issues": ["Contains generic 'to be determined' statements", "Low actionability score - acceptance criteria not specific enough", "Low domain context score - needs more payment processing terminology"]}], "aggregate_metrics": {"average_clarity": 4.0, "average_actionability": 2.0, "average_technical_detail": 4.125, "average_domain_context": 1.75, "average_generic_penalty": 2.0, "overall_average": 2.46875}, "quality_issues": [], "recommendations": []}