=== PROMPT FOR IDEA 2 ===
Original Idea: Clearing instructions are to be sent via Kafka and not SQS
Timestamp: 2025-06-20T19:51:32.916874
Processing Time: 31.40 seconds
==================================================

Transform this payment processing idea into a JIRA ticket. Return only valid JSON.

CONTEXT: SIMILAR TICKETS:
1. MARS-1933: dashboards are not reacting to scheme dropdown change
2. MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
COMMON FILES: internal/operation/operations_requests.go, internal/auth/internal/model/financial_transaction.go, internal/auth/internal/scheme/jcb/converters_events_test.go

IDEA: "Clearing instructions are to be sent via Kafka and not SQS"

Required JSON format:
{
  "title": "Specific actionable title",
  "business_value": "Clear business impact and compliance benefits",
  "acceptance_criteria": ["Specific testable criteria", "Another specific criteria"],
  "open_questions": ["Specific technical question", "Specific business question"],
  "technical_design": "Detailed implementation approach with components and steps",
  "likely_files": ["path/to/file1.java", "path/to/file2.yaml"],
  "impact_assessment": "Risk level and coordination requirements"
}

JSON: