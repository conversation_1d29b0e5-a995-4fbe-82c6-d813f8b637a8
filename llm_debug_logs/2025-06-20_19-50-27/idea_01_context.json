{"idea_number": 1, "original_idea": "I need to block any recurring auths for MC and JCB", "timestamp": "2025-06-20T19:51:01.520224", "context": {"similar_tickets": [{"ticket": {"key": "MARS-1928", "title": "do we need to send any advice to the issuer upon incorrect PIN entry", "description": ""}, "similarity_score": 0.14285714285714285, "file_changes": []}, {"ticket": {"key": "MARS-2421", "title": "AMEX Online PIN ", "description": "Goal is to get carbs auth certified and prod-ready for Amex Online PIN feature"}, "similarity_score": 0.13636363636363635, "file_changes": ["build/helm/auth-amex/Chart.yaml", "build/helm/auth-amex/templates/config.yaml", "build/helm/auth-amex/values.gcp.dev-ld.yaml", "build/helm/auth-amex/values.gcp.prd-ff.yaml", "build/helm/auth-amex/values.gcp.prd-ld.yaml", "build/helm/auth-amex/values.gcp.stg-ff.yaml", "build/helm/auth-amex/values.gcp.stg-ld.yaml", "build/helm/auth-amex/values.yaml", "internal/amex/request_converter.go", "internal/amex/request_converter_test.go", "internal/app/amexapi/app.go", "internal/app/amexapi/config.go", "internal/util/testutil/testdataamex/cert_testcases.go"]}, {"ticket": {"key": "MARS-2622", "title": "MC transactions failed to be stored", "description": ""}, "similarity_score": 0.13333333333333333, "file_changes": []}], "domain_examples": [{"key": "MARS-2814", "title": "Migrate new terminals from already migrated merchants", "description": "*Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals"}, {"key": "MARS-2810", "title": "enable cross-region access from AG txn svc to auth-amex", "description": "[Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there’s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service → auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our  Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure  (dojo-payments-nonprod)  test-global-lb-access ❯ gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*"}], "scheme_examples": [{"key": "MARS-2813", "title": "reduce STAN duplicates generated within same second for MC txns", "description": "*Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we’re not exactly compliant with the above requirement, see <PERSON><PERSON>’s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*"}, {"key": "MARS-2811", "title": "CARBS Terminal Migration on 05/12", "description": "*Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n"}], "components": ["sqs_messaging"]}}