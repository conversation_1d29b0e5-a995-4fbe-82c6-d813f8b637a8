=== PROMPT FOR IDEA 1 ===
Original Idea: I need to block any recurring auths for MC and JCB
Timestamp: 2025-06-20T19:51:01.519854
Processing Time: 32.33 seconds
==================================================

Transform this payment processing idea into a JIRA ticket. Return only valid JSON.

CONTEXT: SIMILAR TICKETS:
1. MARS-1928: do we need to send any advice to the issuer upon incorrect PIN entry
2. MARS-2421: AMEX Online PIN 

IDEA: "I need to block any recurring auths for MC and JCB"

Required JSON format:
{
  "title": "Specific actionable title",
  "business_value": "Clear business impact and compliance benefits",
  "acceptance_criteria": ["Specific testable criteria", "Another specific criteria"],
  "open_questions": ["Specific technical question", "Specific business question"],
  "technical_design": "Detailed implementation approach with components and steps",
  "likely_files": ["path/to/file1.java", "path/to/file2.yaml"],
  "impact_assessment": "Risk level and coordination requirements"
}

JSON: