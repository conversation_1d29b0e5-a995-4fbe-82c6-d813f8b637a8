{"idea_number": 2, "original_idea": "Clearing instructions are to be sent via Kafka and not SQS", "timestamp": "2025-06-20T19:51:32.917236", "context": {"similar_tickets": [{"ticket": {"key": "MARS-1933", "title": "dashboards are not reacting to scheme dropdown change", "description": ""}, "similarity_score": 0.1875, "file_changes": []}, {"ticket": {"key": "MARS-2199", "title": "Investigate Ecom transactions which are being rejected in Clearing.", "description": "Ecom transaction rejected in clearing due to ECI not being set.\nAuth FTID: b2ff52e6-a091-49ec-a8b6-1086c5fa251e\nReversal FTID: 3c1dc1d2-629f-4b5b-b687-800b974f1dc9"}, "similarity_score": 0.14285714285714285, "file_changes": []}, {"ticket": {"key": "MARS-2622", "title": "MC transactions failed to be stored", "description": ""}, "similarity_score": 0.13333333333333333, "file_changes": []}], "domain_examples": [{"key": "MARS-2814", "title": "Migrate new terminals from already migrated merchants", "description": "*Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals"}, {"key": "MARS-2813", "title": "reduce STAN duplicates generated within same second for MC txns", "description": "*Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we’re not exactly compliant with the above requirement, see <PERSON><PERSON>’s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*"}], "file_patterns": ["internal/operation/operations_requests.go", "internal/auth/internal/model/financial_transaction.go", "internal/auth/internal/scheme/jcb/converters_events_test.go", "internal/auth/internal/model/testdata/operationrequest/operation_request.go", "internal/scheme/mastercard/converters_auth_responses.go", "internal/scheme/mastercard/converters_auth_responses_test.go", "events/asyncapi.yaml", "internal/authmc/app.go", "internal/operation/operations_requests_test.go", "internal/auth/internal/scheme/diners/converters_events.go"], "components": ["sqs_messaging"]}}