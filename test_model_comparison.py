#!/usr/bin/env python3
"""
Model Comparison Test for JIRA Ticket Similarity Matrix Generator

This script tests all three supported models (qwen3, alma13, gemma3) to compare:
1. Self-comparison score consistency (should be ≥0.95)
2. Processing speed (seconds per comparison)
3. Overall similarity score quality and reasonableness

Tests are run on real JIRA tickets from enriched_tickets_clean.json.
"""

import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
import statistics

from jira_similarity_matrix import SimilarityMatrixGenerator, SimilarityConfig


def create_test_subset(n_tickets: int = 8) -> str:
    """Create a test subset from real JIRA tickets."""
    test_file = f"model_test_{n_tickets}_tickets.json"
    
    try:
        with open('enriched_tickets_clean.json', 'r', encoding='utf-8') as f:
            tickets = json.load(f)
        
        # Select diverse tickets for better testing
        test_tickets = tickets[:n_tickets]
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_tickets, f, indent=2)
        
        print(f"Created {test_file} with {len(test_tickets)} tickets:")
        for ticket in test_tickets:
            print(f"  {ticket['key']}: {ticket['title'][:60]}...")
        
        return test_file
        
    except Exception as e:
        print(f"Error creating test subset: {e}")
        sys.exit(1)


def test_model(model_preset: str, tickets_file: str, n_tickets: int) -> Dict[str, Any]:
    """Test a specific model and return performance metrics."""
    print(f"\n{'='*60}")
    print(f"TESTING MODEL: {model_preset.upper()}")
    print(f"{'='*60}")
    
    # Configure test
    config = SimilarityConfig(
        tickets_file=Path(tickets_file),
        output_json=Path(f"test_{model_preset}_matrix.json"),
        output_csv=Path(f"test_{model_preset}_matrix.csv"),
        model_preset=model_preset,
        include_files=True,
        temperature=0.1,
        max_retries=2,
        cache_file=Path(f"test_{model_preset}_cache.json")
    )
    
    # Set up logging to capture timing info
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        force=True
    )
    
    try:
        start_time = time.time()
        
        # Run similarity matrix generation
        generator = SimilarityMatrixGenerator(config)
        success = generator.run()

        total_time = time.time() - start_time

        if not success:
            return {"model": model_preset, "success": False, "error": "Matrix generation failed"}
        
        # Load results for analysis
        with open(config.output_json, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # Analyze results
        matrix = results["matrix"]
        metadata = results["metadata"]
        validation = metadata["validation"]
        
        # Calculate metrics
        total_comparisons = n_tickets * n_tickets
        avg_time_per_comparison = total_time / total_comparisons
        
        # Self-comparison analysis
        self_scores = []
        for key in results["ticket_keys"]:
            self_scores.append(matrix[key][key])
        
        # Non-self comparison analysis
        non_self_scores = []
        for key_a in results["ticket_keys"]:
            for key_b in results["ticket_keys"]:
                if key_a != key_b:
                    non_self_scores.append(matrix[key_a][key_b])
        
        # Symmetry analysis
        symmetry_errors = []
        for i, key_a in enumerate(results["ticket_keys"]):
            for j, key_b in enumerate(results["ticket_keys"]):
                if i < j:  # Check each pair only once
                    score_ab = matrix[key_a][key_b]
                    score_ba = matrix[key_b][key_a]
                    error = abs(score_ab - score_ba)
                    symmetry_errors.append(error)
        
        metrics = {
            "model": model_preset,
            "success": True,
            "total_time_seconds": total_time,
            "avg_time_per_comparison": avg_time_per_comparison,
            "total_comparisons": total_comparisons,
            "validation_passed": validation["overall_passed"],
            
            # Self-comparison metrics
            "self_comparison_scores": self_scores,
            "avg_self_score": statistics.mean(self_scores),
            "min_self_score": min(self_scores),
            "max_self_score": max(self_scores),
            "self_scores_above_95": sum(1 for s in self_scores if s >= 0.95),
            "self_score_consistency": statistics.stdev(self_scores) if len(self_scores) > 1 else 0.0,
            
            # Non-self comparison metrics
            "avg_non_self_score": statistics.mean(non_self_scores) if non_self_scores else 0.0,
            "min_non_self_score": min(non_self_scores) if non_self_scores else 0.0,
            "max_non_self_score": max(non_self_scores) if non_self_scores else 0.0,
            "non_self_score_std": statistics.stdev(non_self_scores) if len(non_self_scores) > 1 else 0.0,
            
            # Symmetry metrics
            "avg_symmetry_error": statistics.mean(symmetry_errors) if symmetry_errors else 0.0,
            "max_symmetry_error": max(symmetry_errors) if symmetry_errors else 0.0,
            "symmetry_violations": sum(1 for e in symmetry_errors if e > 0.05),
            
            # Validation details
            "validation_details": validation
        }
        
        return metrics
        
    except Exception as e:
        print(f"Error testing {model_preset}: {e}")
        return {
            "model": model_preset,
            "success": False,
            "error": str(e)
        }


def print_model_results(metrics: Dict[str, Any]):
    """Print detailed results for a model."""
    if not metrics["success"]:
        print(f"❌ {metrics['model'].upper()} FAILED: {metrics.get('error', 'Unknown error')}")
        return
    
    model = metrics["model"].upper()
    print(f"\n📊 {model} RESULTS:")
    print(f"  ⏱️  Total time: {metrics['total_time_seconds']:.1f}s")
    print(f"  🚀 Avg time per comparison: {metrics['avg_time_per_comparison']:.2f}s")
    print(f"  ✅ Validation passed: {'Yes' if metrics['validation_passed'] else 'No'}")
    
    print(f"\n  🎯 Self-Comparison Analysis:")
    print(f"     Average score: {metrics['avg_self_score']:.3f}")
    print(f"     Min score: {metrics['min_self_score']:.3f}")
    print(f"     Max score: {metrics['max_self_score']:.3f}")
    print(f"     Scores ≥0.95: {metrics['self_scores_above_95']}/{len(metrics['self_comparison_scores'])}")
    print(f"     Consistency (std dev): {metrics['self_score_consistency']:.3f}")
    
    print(f"\n  📈 Non-Self Comparison Analysis:")
    print(f"     Average score: {metrics['avg_non_self_score']:.3f}")
    print(f"     Score range: {metrics['min_non_self_score']:.3f} - {metrics['max_non_self_score']:.3f}")
    print(f"     Score std dev: {metrics['non_self_score_std']:.3f}")
    
    print(f"\n  🔄 Symmetry Analysis:")
    print(f"     Avg symmetry error: {metrics['avg_symmetry_error']:.4f}")
    print(f"     Max symmetry error: {metrics['max_symmetry_error']:.4f}")
    print(f"     Symmetry violations (>0.05): {metrics['symmetry_violations']}")


def compare_models(results: List[Dict[str, Any]]):
    """Compare results across all models."""
    print(f"\n{'='*60}")
    print("MODEL COMPARISON SUMMARY")
    print(f"{'='*60}")
    
    successful_results = [r for r in results if r["success"]]
    
    if not successful_results:
        print("❌ No successful model tests to compare")
        return
    
    print(f"\n📊 PERFORMANCE COMPARISON:")
    print(f"{'Model':<10} {'Time/Comp':<12} {'Self Avg':<10} {'Self ≥0.95':<12} {'Symmetry':<10}")
    print("-" * 60)
    
    for result in successful_results:
        model = result["model"]
        time_per_comp = f"{result['avg_time_per_comparison']:.2f}s"
        self_avg = f"{result['avg_self_score']:.3f}"
        self_good = f"{result['self_scores_above_95']}/{len(result['self_comparison_scores'])}"
        symmetry = f"{result['avg_symmetry_error']:.4f}"
        
        print(f"{model:<10} {time_per_comp:<12} {self_avg:<10} {self_good:<12} {symmetry:<10}")
    
    # Find best performers
    fastest = min(successful_results, key=lambda x: x['avg_time_per_comparison'])
    most_consistent = min(successful_results, key=lambda x: x['self_score_consistency'])
    highest_self = max(successful_results, key=lambda x: x['avg_self_score'])
    
    print(f"\n🏆 BEST PERFORMERS:")
    print(f"  🚀 Fastest: {fastest['model']} ({fastest['avg_time_per_comparison']:.2f}s per comparison)")
    print(f"  🎯 Most consistent self-scores: {most_consistent['model']} (std dev: {most_consistent['self_score_consistency']:.3f})")
    print(f"  📈 Highest self-scores: {highest_self['model']} (avg: {highest_self['avg_self_score']:.3f})")
    
    # Quality assessment
    print(f"\n🔍 QUALITY ASSESSMENT:")
    for result in successful_results:
        model = result["model"]
        issues = []
        
        if result['avg_self_score'] < 0.95:
            issues.append(f"Low self-comparison avg ({result['avg_self_score']:.3f})")
        
        if result['self_scores_above_95'] < len(result['self_comparison_scores']):
            issues.append(f"Some self-scores <0.95")
        
        if result['avg_symmetry_error'] > 0.02:
            issues.append(f"High symmetry error ({result['avg_symmetry_error']:.4f})")
        
        if result['symmetry_violations'] > 0:
            issues.append(f"{result['symmetry_violations']} symmetry violations")
        
        status = "✅ Excellent" if not issues else f"⚠️  Issues: {'; '.join(issues)}"
        print(f"  {model}: {status}")


def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Compare all MLX models for JIRA similarity matrix generation")
    parser.add_argument("--tickets", type=int, default=8, help="Number of tickets to test with")
    parser.add_argument("--models", nargs="+", choices=["qwen3", "alma13", "gemma3"], 
                       default=["qwen3", "alma13", "gemma3"], help="Models to test")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test files after completion")
    
    args = parser.parse_args()
    
    print("🧪 JIRA Ticket Similarity Matrix - Model Comparison Test")
    print(f"Testing {len(args.models)} models with {args.tickets} tickets")
    
    # Create test subset
    test_file = create_test_subset(args.tickets)
    
    # Test each model
    results = []
    for model in args.models:
        try:
            result = test_model(model, test_file, args.tickets)
            results.append(result)
            print_model_results(result)
        except KeyboardInterrupt:
            print(f"\n⚠️  Testing interrupted during {model}")
            break
        except Exception as e:
            print(f"❌ Unexpected error testing {model}: {e}")
            results.append({"model": model, "success": False, "error": str(e)})
    
    # Compare results
    if len(results) > 1:
        compare_models(results)
    
    # Cleanup if requested
    if args.cleanup:
        print(f"\n🧹 Cleaning up test files...")
        import glob
        for pattern in ["test_*_matrix.*", "test_*_cache.json", f"model_test_{args.tickets}_tickets.json"]:
            for file in glob.glob(pattern):
                try:
                    Path(file).unlink()
                    print(f"  Removed {file}")
                except:
                    pass
    
    print(f"\n✅ Model comparison test completed!")


if __name__ == "__main__":
    main()
