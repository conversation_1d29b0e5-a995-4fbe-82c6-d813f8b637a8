=== PROMPT FOR IDEA 3 ===
Original Idea: Amex wants a reversal local datetime to be exactly like the time on a terminal
Timestamp: 2025-06-20T19:52:05.687270
Processing Time: 32.77 seconds
==================================================

Transform this payment processing idea into a JIRA ticket. Return only valid JSON.

CONTEXT: SIMILAR TICKETS:
1. MARS-2745: use correct reversal reason values for MC
2. MARS-2400: AMEX tech debt
COMMON FILES: internal/auth/internal/scheme/mc/converters_reversal_requests_test.go, internal/auth/internal/scheme/mc/converters_reversal_requests.go, componenttest/amexapi/request_v2.go

IDEA: "Amex wants a reversal local datetime to be exactly like the time on a terminal"

Required JSON format:
{
  "title": "Specific actionable title",
  "business_value": "Clear business impact and compliance benefits",
  "acceptance_criteria": ["Specific testable criteria", "Another specific criteria"],
  "open_questions": ["Specific technical question", "Specific business question"],
  "technical_design": "Detailed implementation approach with components and steps",
  "likely_files": ["path/to/file1.java", "path/to/file2.yaml"],
  "impact_assessment": "Risk level and coordination requirements"
}

JSON: