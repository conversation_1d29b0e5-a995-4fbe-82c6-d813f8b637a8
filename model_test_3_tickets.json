[{"key": "MARS-2814", "title": "Migrate new terminals from already migrated merchants", "description": "*Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals", "files_changed": []}, {"key": "MARS-2813", "title": "reduce STAN duplicates generated within same second for MC txns", "description": "*Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we’re not exactly compliant with the above requirement, see <PERSON><PERSON>’s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*", "files_changed": []}, {"key": "MARS-2812", "title": "Create diners KafkaConnector in prod", "description": "*Business Value:*\n\n*Acceptance Criteria:*", "files_changed": []}]