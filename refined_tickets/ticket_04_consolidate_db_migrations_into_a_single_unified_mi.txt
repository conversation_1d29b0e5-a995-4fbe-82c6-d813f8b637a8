# Consolidate DB migrations into a single unified migration

**Original Idea:** Squash all our DB migrations into a simpler big one

*Business Value:*
Simplifies database schema management and reduces compliance risks by centralizing all migration logic into one file.

*Acceptance Criteria:*
* All existing migrations are successfully merged into the new unified migration
* The new migration file passes all test cases in migrations_test.go

*Open Questions:*
* How to handle versioning and rollback strategies for the unified migration?
* What are the compliance implications of merging multiple migrations into one?

*Technical Design:*
Create a new migration file that includes all SQL statements from existing migrations. Update the test suite to verify the combined migration's correctness. Ensure that the new migration handles dependencies and order of operations correctly.

*Impact and Notifications:*
Low risk if properly tested. Requires coordination with the database team to validate schema changes.

*Likely Files to Modify:*

* internal/database/migrations/db_migration.go
* internal/database/migrations/test_db_migration.go

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined