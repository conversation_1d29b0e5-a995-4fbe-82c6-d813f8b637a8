#!/usr/bin/env python3
"""
Test script for JIRA Ticket Similarity Matrix Generator

This script provides basic testing and validation for the similarity matrix generator.
It can be used to test with a small subset of tickets before running on the full dataset.
"""

import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any

from jira_similarity_matrix import SimilarityMatrixGenerator, SimilarityConfig


def create_test_tickets(n_tickets: int = 5) -> List[Dict[str, Any]]:
    """Create a small set of test tickets for validation."""
    test_tickets = [
        {
            "key": "TEST-001",
            "title": "Implement Visa authorization endpoint",
            "description": "Create new REST endpoint for Visa card authorization processing. This endpoint should handle authorization requests, validate card data, and return appropriate responses.",
            "files_changed": [
                {"file_path": "internal/auth/app/authvisa/app.go", "change_type": "M"},
                {"file_path": "internal/auth/internal/scheme/visa/operations.go", "change_type": "M"}
            ]
        },
        {
            "key": "TEST-002", 
            "title": "Add Visa authorization validation",
            "description": "Implement validation rules for Visa authorization requests. Should validate card number format, expiry date, and amount limits according to Visa specifications.",
            "files_changed": [
                {"file_path": "internal/auth/internal/validation/visa_rules.go", "change_type": "A"},
                {"file_path": "internal/auth/internal/validation/rules_test.go", "change_type": "M"}
            ]
        },
        {
            "key": "TEST-003",
            "title": "Fix Mastercard reversal processing",
            "description": "Bug fix for Mastercard reversal transactions. The current implementation doesn't properly handle partial reversals and needs to be updated to match MC specifications.",
            "files_changed": [
                {"file_path": "internal/auth/internal/scheme/mc/reversal.go", "change_type": "M"},
                {"file_path": "internal/auth/internal/scheme/mc/reversal_test.go", "change_type": "M"}
            ]
        },
        {
            "key": "TEST-004",
            "title": "Update Grafana dashboard panels",
            "description": "Add new monitoring panels to the Grafana dashboard for tracking transaction success rates and latency metrics. This is for operational visibility.",
            "files_changed": [
                {"file_path": "monitoring/grafana/dashboards/auth-dashboard.json", "change_type": "M"}
            ]
        },
        {
            "key": "TEST-005",
            "title": "Implement Visa reversal endpoint", 
            "description": "Create REST endpoint for Visa transaction reversals. This should handle both full and partial reversals according to Visa network specifications.",
            "files_changed": [
                {"file_path": "internal/auth/app/authvisa/app.go", "change_type": "M"},
                {"file_path": "internal/auth/internal/scheme/visa/reversal.go", "change_type": "A"}
            ]
        }
    ]
    
    return test_tickets[:n_tickets]


def save_test_tickets(tickets: List[Dict[str, Any]], filename: str = "test_tickets.json"):
    """Save test tickets to a JSON file."""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(tickets, f, indent=2, ensure_ascii=False)
    print(f"Saved {len(tickets)} test tickets to {filename}")


def analyze_test_results(matrix_file: str = "similarity_matrix_output.json"):
    """Analyze and display test results."""
    try:
        with open(matrix_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        matrix = data["matrix"]
        metadata = data["metadata"]
        validation = metadata["validation"]
        
        print("\n" + "="*60)
        print("SIMILARITY MATRIX TEST RESULTS")
        print("="*60)
        
        print(f"Tickets processed: {metadata['n_tickets']}")
        print(f"Total comparisons: {metadata['total_comparisons']}")
        print(f"Generation time: {metadata['generation_time_seconds']:.2f} seconds")
        print(f"Model used: {metadata['model_preset']}")
        print(f"Include files: {metadata['include_files']}")
        
        print("\nValidation Results:")
        print(f"  Overall: {'PASS' if validation['overall_passed'] else 'FAIL'}")
        print(f"  Self-comparison: {'PASS' if validation['self_comparison_check']['passed'] else 'FAIL'}")
        print(f"  Symmetry: {'PASS' if validation['symmetry_check']['passed'] else 'FAIL'}")
        print(f"  Score range: {'PASS' if validation['score_range_check']['passed'] else 'FAIL'}")
        print(f"  Completeness: {'PASS' if validation['completeness_check']['passed'] else 'FAIL'}")
        
        # Show some interesting similarity scores
        print("\nInteresting Similarity Scores:")
        ticket_keys = data["ticket_keys"]
        
        # Find highest non-self similarities
        similarities = []
        for key_a in ticket_keys:
            for key_b in ticket_keys:
                if key_a != key_b:
                    score = matrix[key_a][key_b]
                    similarities.append((key_a, key_b, score))
        
        # Sort by score descending
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        print("\nTop 5 Most Similar Ticket Pairs:")
        for i, (key_a, key_b, score) in enumerate(similarities[:5]):
            print(f"  {i+1}. {key_a} ↔ {key_b}: {score:.3f}")
        
        print("\nSelf-Comparison Scores:")
        for key in ticket_keys:
            self_score = matrix[key][key]
            status = "✓" if self_score >= 0.95 else "✗"
            print(f"  {key}: {self_score:.3f} {status}")
        
        # Check for potential issues
        issues = []
        
        # Check self-comparison scores
        for key in ticket_keys:
            if matrix[key][key] < 0.95:
                issues.append(f"Low self-comparison score for {key}: {matrix[key][key]:.3f}")
        
        # Check symmetry
        for key_a in ticket_keys:
            for key_b in ticket_keys:
                if key_a < key_b:  # Check each pair only once
                    score_ab = matrix[key_a][key_b]
                    score_ba = matrix[key_b][key_a]
                    if abs(score_ab - score_ba) > 0.05:
                        issues.append(f"Symmetry violation: {key_a}↔{key_b} = {score_ab:.3f}, {key_b}↔{key_a} = {score_ba:.3f}")
        
        if issues:
            print(f"\nPotential Issues Found ({len(issues)}):")
            for issue in issues[:10]:  # Show first 10 issues
                print(f"  ⚠ {issue}")
        else:
            print("\n✓ No issues found!")
        
        return validation['overall_passed']
        
    except Exception as e:
        print(f"Error analyzing test results: {e}")
        return False


def run_test(n_tickets: int = 5, model_preset: str = "qwen3"):
    """Run a complete test of the similarity matrix generator."""
    print(f"Running similarity matrix test with {n_tickets} tickets...")
    
    # Create test tickets
    test_tickets = create_test_tickets(n_tickets)
    test_file = "test_tickets.json"
    save_test_tickets(test_tickets, test_file)
    
    # Configure test
    config = SimilarityConfig(
        tickets_file=Path(test_file),
        output_json=Path("test_similarity_matrix.json"),
        output_csv=Path("test_similarity_matrix.csv"),
        model_preset=model_preset,
        include_files=True,
        temperature=0.1,
        max_retries=2,
        cache_file=Path("test_similarity_cache.json")
    )
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Run similarity matrix generation
        generator = SimilarityMatrixGenerator(config)
        success = generator.run()
        
        if success:
            # Analyze results
            analysis_success = analyze_test_results("test_similarity_matrix.json")
            
            if analysis_success:
                print("\n✅ Test completed successfully!")
                return True
            else:
                print("\n❌ Test completed but validation failed!")
                return False
        else:
            print("\n❌ Test failed during matrix generation!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
    finally:
        # Clean up test files
        for file in [test_file, "test_similarity_cache.json"]:
            try:
                Path(file).unlink(missing_ok=True)
            except:
                pass


def main():
    """Main test entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test JIRA similarity matrix generator")
    parser.add_argument("--tickets", type=int, default=5, help="Number of test tickets to use")
    parser.add_argument("--model", choices=["gemma3", "qwen3", "alma13"], default="qwen3", help="Model preset to test")
    parser.add_argument("--analyze-only", action="store_true", help="Only analyze existing results")
    
    args = parser.parse_args()
    
    if args.analyze_only:
        success = analyze_test_results()
        sys.exit(0 if success else 1)
    else:
        success = run_test(args.tickets, args.model)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
