IDEA REFINER IMPROVEMENT RECOMMENDATIONS
==================================================

1. PROMPT IMPROVEMENT: Enhance prompt to request more specific, measurable language. Add examples of specific vs generic statements.

2. ACCEPTANCE CRITERIA: Improve prompt to generate more actionable acceptance criteria. Request 'Given-When-Then' format and specific validation steps.

3. DOMAIN KNOWLEDGE: Improve payment processing domain context in prompts. Add more scheme-specific terminology and industry standards.

4. PERFORMANCE: Average processing time is 31.2s per idea. Consider optimizing context size or using a faster model.

