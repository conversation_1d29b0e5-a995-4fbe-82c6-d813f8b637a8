#!/usr/bin/env python3
"""
JIRA Ticket Similarity Matrix Generator using MLX-based LLM

This script generates a similarity matrix for JIRA tickets using an MLX-based LLM
following the patterns established in idea_refiner.py and translate3_mlx_2.py.

Key Features:
- Loads JIRA tickets from enriched_tickets_clean.json
- Uses MLX model with JSON-only output and /no_think directive
- Compares ticket descriptions and optionally file changes
- Generates N×N similarity matrix with validation
- Supports progress tracking and caching for efficiency
- Validates matrix symmetry and self-comparison scores
"""

import argparse
import json
import logging
import math
import sys
import time
from dataclasses import dataclass, asdict
from functools import lru_cache
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import csv

# Import MLX components for LLM integration
try:
    from mlx_lm import load, generate
    from mlx_lm.sample_utils import make_sampler, make_logits_processors
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    logging.warning("MLX not available - similarity matrix generation requires MLX")

# Model presets with context windows (from translate3_mlx_2.py)
PRESETS = {
    "gemma3": "/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
    "qwen3": "/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
    "alma13": "/Users/<USER>/.lmstudio/models/mlx-community/ALMA-13B-R-4bit-mlx",
}

# Context windows for each model
MAX_CONTEXT = {
    "gemma3": 8192,
    "qwen3": 32768,
    "alma13": 4096,
}


@dataclass
class SimilarityConfig:
    """Configuration for similarity matrix generation."""
    tickets_file: Path
    output_json: Path
    output_csv: Path
    model_preset: str
    include_files: bool = True
    temperature: float = 0.1  # Low temperature for consistent scoring
    max_retries: int = 2
    cache_file: Optional[Path] = None
    batch_size: int = 10  # For progress tracking


@dataclass
class TicketPair:
    """A pair of tickets for similarity comparison."""
    ticket_a: Dict[str, Any]
    ticket_b: Dict[str, Any]
    key_a: str
    key_b: str


@dataclass
class SimilarityResult:
    """Result of similarity comparison between two tickets."""
    key_a: str
    key_b: str
    similarity_score: float
    reasoning: Optional[str] = None
    processing_time: float = 0.0


class SimilarityMatrixGenerator:
    """Generates similarity matrix for JIRA tickets using MLX-based LLM."""

    def __init__(self, config: SimilarityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        if not MLX_AVAILABLE:
            raise RuntimeError("MLX required for similarity matrix generation")
        
        # Load MLX model
        self._load_model()
        
        # Load tickets
        self.tickets = self._load_tickets()
        self.ticket_keys = [ticket['key'] for ticket in self.tickets]
        self.n_tickets = len(self.tickets)
        
        # Initialize cache
        self.cache = self._load_cache() if config.cache_file else {}
        
        self.logger.info(f"Loaded {self.n_tickets} tickets for similarity matrix generation")
        self.logger.info(f"Total comparisons needed: {self.n_tickets * self.n_tickets}")

    def _load_model(self):
        """Load MLX model following translate3_mlx_2.py patterns."""
        try:
            model_path = PRESETS[self.config.model_preset]
            self.logger.info(f"Loading {self.config.model_preset} model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.max_context = MAX_CONTEXT.get(self.config.model_preset, 4096)
            self.logger.info(f"Model loaded successfully (context: {self.max_context} tokens)")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    def _load_tickets(self) -> List[Dict[str, Any]]:
        """Load JIRA tickets from enriched_tickets_clean.json."""
        try:
            with open(self.config.tickets_file, 'r', encoding='utf-8') as f:
                tickets = json.load(f)
            self.logger.info(f"Loaded {len(tickets)} tickets from {self.config.tickets_file}")
            return tickets
        except Exception as e:
            self.logger.error(f"Failed to load tickets: {e}")
            raise

    def _load_cache(self) -> Dict[str, float]:
        """Load cached similarity scores if available."""
        if not self.config.cache_file or not self.config.cache_file.exists():
            return {}
        
        try:
            with open(self.config.cache_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)
            self.logger.info(f"Loaded {len(cache)} cached similarity scores")
            return cache
        except Exception as e:
            self.logger.warning(f"Failed to load cache: {e}")
            return {}

    def _save_cache(self):
        """Save similarity scores to cache file."""
        if not self.config.cache_file:
            return
        
        try:
            with open(self.config.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, indent=2)
            self.logger.info(f"Saved {len(self.cache)} similarity scores to cache")
        except Exception as e:
            self.logger.warning(f"Failed to save cache: {e}")

    @lru_cache(maxsize=1000)
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using model tokenizer."""
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            return len(text) // 4  # Rough estimate

    def _create_similarity_prompt(self, ticket_a: Dict[str, Any], ticket_b: Dict[str, Any]) -> str:
        """Create prompt for LLM to compare two tickets and return similarity score."""
        
        # Extract ticket information
        title_a = ticket_a.get('title', '')
        desc_a = ticket_a.get('description', '')
        files_a = ticket_a.get('files_changed', [])
        
        title_b = ticket_b.get('title', '')
        desc_b = ticket_b.get('description', '')
        files_b = ticket_b.get('files_changed', [])
        
        # Build file change context if enabled
        files_context = ""
        if self.config.include_files and (files_a or files_b):
            files_a_str = ", ".join([f['file_path'] for f in files_a]) if files_a else "No files changed"
            files_b_str = ", ".join([f['file_path'] for f in files_b]) if files_b else "No files changed"
            files_context = f"""
Files changed in Ticket A: {files_a_str}
Files changed in Ticket B: {files_b_str}
"""

        prompt = f"""You are an expert software engineer analyzing JIRA tickets for a payment processing system. Your task is to compare two tickets and determine their similarity.

/no_think

Compare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:

**Ticket A ({ticket_a.get('key', 'Unknown')}):**
Title: {title_a}
Description: {desc_a[:1000]}...{files_context}

**Ticket B ({ticket_b.get('key', 'Unknown')}):**
Title: {title_b}
Description: {desc_b[:1000]}...

Consider these factors:
1. Functional similarity (same feature/component)
2. Technical similarity (similar implementation approach)
3. Domain similarity (same payment scheme, same type of work)
4. File overlap (if files are changed in similar areas)

Scoring guidelines:
- 1.0: Identical or nearly identical tickets
- 0.8-0.9: Very similar functionality or implementation
- 0.6-0.7: Related work in same domain/component
- 0.4-0.5: Some overlap but different focus
- 0.2-0.3: Minimal similarity
- 0.0-0.1: Completely unrelated

IMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.

{{
    "similarity_score": 0.75,
    "reasoning": "Brief explanation of the similarity assessment"
}}"""

        return prompt

    def _generate_similarity_score(self, ticket_a: Dict[str, Any], ticket_b: Dict[str, Any]) -> SimilarityResult:
        """Generate similarity score between two tickets using LLM."""
        start_time = time.time()

        key_a = ticket_a.get('key', 'Unknown')
        key_b = ticket_b.get('key', 'Unknown')

        # Special case: self-comparison should always be 1.0
        if key_a == key_b:
            processing_time = time.time() - start_time
            return SimilarityResult(key_a, key_b, 1.0, "Self-comparison", processing_time)

        # Check cache first
        cache_key = f"{key_a}:{key_b}"
        reverse_cache_key = f"{key_b}:{key_a}"

        if cache_key in self.cache:
            score = self.cache[cache_key]
            processing_time = time.time() - start_time
            return SimilarityResult(key_a, key_b, score, "From cache", processing_time)

        if reverse_cache_key in self.cache:
            score = self.cache[reverse_cache_key]
            self.cache[cache_key] = score  # Store both directions
            processing_time = time.time() - start_time
            return SimilarityResult(key_a, key_b, score, "From cache (reverse)", processing_time)
        
        # Generate new similarity score
        for attempt in range(self.config.max_retries + 1):
            try:
                prompt = self._create_similarity_prompt(ticket_a, ticket_b)
                
                # Generate response using MLX with repetition penalty (from translate3_mlx_2.py)
                sampler = make_sampler(temp=self.config.temperature, top_p=0.9)
                processors = make_logits_processors(repetition_penalty=1.05, repetition_context_size=256)
                
                response = generate(
                    self.model,
                    self.tokenizer,
                    prompt=prompt,
                    max_tokens=200,
                    sampler=sampler,
                    logits_processors=processors,
                    verbose=False
                )
                
                # Parse JSON response
                result = self._parse_similarity_response(response, key_a, key_b)
                
                # Cache the result
                self.cache[cache_key] = result.similarity_score
                
                processing_time = time.time() - start_time
                result.processing_time = processing_time
                
                return result
                
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {key_a} vs {key_b}: {e}")
                if attempt == self.config.max_retries:
                    # Return default similarity score on failure
                    processing_time = time.time() - start_time
                    return SimilarityResult(key_a, key_b, 0.0, f"Failed after {self.config.max_retries + 1} attempts", processing_time)

    def _parse_similarity_response(self, response: str, key_a: str, key_b: str) -> SimilarityResult:
        """Parse LLM response to extract similarity score."""
        try:
            # Clean response and extract JSON
            response = response.strip()

            # Try to find the first complete JSON object
            start_idx = response.find('{')
            if start_idx == -1:
                raise ValueError("No JSON object found in response")

            # Find the matching closing brace
            brace_count = 0
            end_idx = -1
            for i in range(start_idx, len(response)):
                if response[i] == '{':
                    brace_count += 1
                elif response[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break

            if end_idx == -1:
                raise ValueError("No complete JSON object found in response")

            json_str = response[start_idx:end_idx]
            data = json.loads(json_str)

            similarity_score = float(data.get('similarity_score', 0.0))
            reasoning = data.get('reasoning', 'No reasoning provided')

            # Validate score range
            if not (0.0 <= similarity_score <= 1.0):
                self.logger.warning(f"Invalid similarity score {similarity_score}, clamping to [0.0, 1.0]")
                similarity_score = max(0.0, min(1.0, similarity_score))

            return SimilarityResult(key_a, key_b, similarity_score, reasoning)

        except Exception as e:
            self.logger.error(f"Failed to parse similarity response for {key_a} vs {key_b}: {e}")
            self.logger.error(f"Response was: {response[:500]}...")  # Truncate long responses
            raise ValueError(f"Failed to parse similarity response: {e}")

    def generate_similarity_matrix(self) -> Dict[str, Any]:
        """Generate complete similarity matrix for all tickets."""
        self.logger.info("Starting similarity matrix generation...")

        # Initialize matrix
        matrix = {}
        total_comparisons = self.n_tickets * self.n_tickets
        completed_comparisons = 0

        # Track validation metrics
        self_comparison_scores = []
        symmetric_violations = []

        start_time = time.time()

        try:
            # Progress tracking with tqdm if available
            try:
                from tqdm import tqdm
                use_tqdm = True
            except ImportError:
                use_tqdm = False
                self.logger.info("tqdm not available, using basic progress tracking")

            # Generate all ticket pairs
            ticket_pairs = []
            for i, ticket_a in enumerate(self.tickets):
                for j, ticket_b in enumerate(self.tickets):
                    ticket_pairs.append((i, j, ticket_a, ticket_b))

            if use_tqdm:
                iterator = tqdm(ticket_pairs, desc="Computing similarities", total=total_comparisons)
            else:
                iterator = ticket_pairs

            for i, j, ticket_a, ticket_b in iterator:
                key_a = ticket_a['key']
                key_b = ticket_b['key']

                # Generate similarity score
                result = self._generate_similarity_score(ticket_a, ticket_b)

                # Store in matrix
                if key_a not in matrix:
                    matrix[key_a] = {}
                matrix[key_a][key_b] = result.similarity_score

                # Track validation metrics
                if key_a == key_b:
                    self_comparison_scores.append(result.similarity_score)

                completed_comparisons += 1

                # Progress logging (if not using tqdm)
                if not use_tqdm and completed_comparisons % 100 == 0:
                    progress = (completed_comparisons / total_comparisons) * 100
                    elapsed = time.time() - start_time
                    eta = (elapsed / completed_comparisons) * (total_comparisons - completed_comparisons)
                    self.logger.info(f"Progress: {progress:.1f}% ({completed_comparisons}/{total_comparisons}), ETA: {eta/60:.1f}min")

                # Save cache periodically
                if completed_comparisons % 500 == 0:
                    self._save_cache()

        except KeyboardInterrupt:
            self.logger.warning("Matrix generation interrupted by user")
            self._save_cache()
            raise

        # Final cache save
        self._save_cache()

        # Validate matrix
        validation_results = self._validate_matrix(matrix, self_comparison_scores)

        total_time = time.time() - start_time
        self.logger.info(f"Similarity matrix generation completed in {total_time/60:.1f} minutes")

        return {
            "matrix": matrix,
            "metadata": {
                "n_tickets": self.n_tickets,
                "total_comparisons": total_comparisons,
                "generation_time_seconds": total_time,
                "model_preset": self.config.model_preset,
                "include_files": self.config.include_files,
                "validation": validation_results
            },
            "ticket_keys": self.ticket_keys
        }

    def _validate_matrix(self, matrix: Dict[str, Dict[str, float]], self_scores: List[float]) -> Dict[str, Any]:
        """Validate the similarity matrix for correctness."""
        self.logger.info("Validating similarity matrix...")

        validation_results = {
            "self_comparison_check": {},
            "symmetry_check": {},
            "score_range_check": {},
            "completeness_check": {}
        }

        # 1. Self-comparison validation (should be ≥0.95)
        valid_self_scores = [score for score in self_scores if score >= 0.95]
        validation_results["self_comparison_check"] = {
            "total_self_comparisons": len(self_scores),
            "valid_self_comparisons": len(valid_self_scores),
            "invalid_self_comparisons": len(self_scores) - len(valid_self_scores),
            "average_self_score": sum(self_scores) / len(self_scores) if self_scores else 0.0,
            "min_self_score": min(self_scores) if self_scores else 0.0,
            "passed": len(valid_self_scores) / len(self_scores) >= 0.9 if self_scores else False
        }

        # 2. Symmetry validation
        symmetry_violations = []
        total_pairs = 0

        for key_a in matrix:
            for key_b in matrix[key_a]:
                if key_a != key_b and key_b in matrix and key_a in matrix[key_b]:
                    total_pairs += 1
                    score_ab = matrix[key_a][key_b]
                    score_ba = matrix[key_b][key_a]

                    # Allow small tolerance for floating point differences
                    if abs(score_ab - score_ba) > 0.05:
                        symmetry_violations.append({
                            "pair": f"{key_a}:{key_b}",
                            "score_ab": score_ab,
                            "score_ba": score_ba,
                            "difference": abs(score_ab - score_ba)
                        })

        validation_results["symmetry_check"] = {
            "total_symmetric_pairs": total_pairs // 2,  # Each pair counted twice
            "symmetry_violations": len(symmetry_violations),
            "violation_rate": len(symmetry_violations) / (total_pairs // 2) if total_pairs > 0 else 0.0,
            "passed": len(symmetry_violations) / (total_pairs // 2) < 0.05 if total_pairs > 0 else True,
            "violations": symmetry_violations[:10]  # Show first 10 violations
        }

        # 3. Score range validation
        all_scores = []
        invalid_scores = []

        for key_a in matrix:
            for key_b in matrix[key_a]:
                score = matrix[key_a][key_b]
                all_scores.append(score)
                if not (0.0 <= score <= 1.0):
                    invalid_scores.append({"pair": f"{key_a}:{key_b}", "score": score})

        validation_results["score_range_check"] = {
            "total_scores": len(all_scores),
            "invalid_scores": len(invalid_scores),
            "average_score": sum(all_scores) / len(all_scores) if all_scores else 0.0,
            "min_score": min(all_scores) if all_scores else 0.0,
            "max_score": max(all_scores) if all_scores else 0.0,
            "passed": len(invalid_scores) == 0,
            "invalid_examples": invalid_scores[:5]
        }

        # 4. Completeness validation
        expected_pairs = self.n_tickets * self.n_tickets
        actual_pairs = len(all_scores)

        validation_results["completeness_check"] = {
            "expected_pairs": expected_pairs,
            "actual_pairs": actual_pairs,
            "completeness_rate": actual_pairs / expected_pairs if expected_pairs > 0 else 0.0,
            "passed": actual_pairs == expected_pairs
        }

        # Overall validation status
        all_checks_passed = all([
            validation_results["self_comparison_check"]["passed"],
            validation_results["symmetry_check"]["passed"],
            validation_results["score_range_check"]["passed"],
            validation_results["completeness_check"]["passed"]
        ])

        validation_results["overall_passed"] = all_checks_passed

        # Log validation summary
        self.logger.info(f"Validation Results:")
        self.logger.info(f"  Self-comparison: {'PASS' if validation_results['self_comparison_check']['passed'] else 'FAIL'}")
        self.logger.info(f"  Symmetry: {'PASS' if validation_results['symmetry_check']['passed'] else 'FAIL'}")
        self.logger.info(f"  Score range: {'PASS' if validation_results['score_range_check']['passed'] else 'FAIL'}")
        self.logger.info(f"  Completeness: {'PASS' if validation_results['completeness_check']['passed'] else 'FAIL'}")
        self.logger.info(f"  Overall: {'PASS' if all_checks_passed else 'FAIL'}")

        return validation_results

    def save_outputs(self, matrix_data: Dict[str, Any]):
        """Save similarity matrix to JSON and CSV formats."""
        self.logger.info("Saving similarity matrix outputs...")

        # Save JSON format
        try:
            with open(self.config.output_json, 'w', encoding='utf-8') as f:
                json.dump(matrix_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"JSON output saved to {self.config.output_json}")
        except Exception as e:
            self.logger.error(f"Failed to save JSON output: {e}")
            raise

        # Save CSV format
        try:
            matrix = matrix_data["matrix"]
            ticket_keys = matrix_data["ticket_keys"]

            with open(self.config.output_csv, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # Write header row
                header = ['ticket_key'] + ticket_keys
                writer.writerow(header)

                # Write matrix rows
                for key_a in ticket_keys:
                    row = [key_a]
                    for key_b in ticket_keys:
                        score = matrix.get(key_a, {}).get(key_b, 0.0)
                        row.append(f"{score:.3f}")
                    writer.writerow(row)

            self.logger.info(f"CSV output saved to {self.config.output_csv}")
        except Exception as e:
            self.logger.error(f"Failed to save CSV output: {e}")
            raise

    def run(self):
        """Run the complete similarity matrix generation process."""
        self.logger.info("Starting JIRA ticket similarity matrix generation")
        self.logger.info(f"Configuration:")
        self.logger.info(f"  Tickets file: {self.config.tickets_file}")
        self.logger.info(f"  Model preset: {self.config.model_preset}")
        self.logger.info(f"  Include files: {self.config.include_files}")
        self.logger.info(f"  Temperature: {self.config.temperature}")
        self.logger.info(f"  Output JSON: {self.config.output_json}")
        self.logger.info(f"  Output CSV: {self.config.output_csv}")

        try:
            # Generate similarity matrix
            matrix_data = self.generate_similarity_matrix()

            # Save outputs
            self.save_outputs(matrix_data)

            # Print summary
            metadata = matrix_data["metadata"]
            validation = metadata["validation"]

            self.logger.info("=" * 60)
            self.logger.info("SIMILARITY MATRIX GENERATION COMPLETE")
            self.logger.info("=" * 60)
            self.logger.info(f"Tickets processed: {metadata['n_tickets']}")
            self.logger.info(f"Total comparisons: {metadata['total_comparisons']}")
            self.logger.info(f"Generation time: {metadata['generation_time_seconds']/60:.1f} minutes")
            self.logger.info(f"Validation status: {'PASS' if validation['overall_passed'] else 'FAIL'}")

            if not validation['overall_passed']:
                self.logger.warning("Matrix validation failed - please review the validation results")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Similarity matrix generation failed: {e}")
            raise


def parse_arguments() -> SimilarityConfig:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate similarity matrix for JIRA tickets using MLX-based LLM",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--tickets",
        type=Path,
        default="enriched_tickets_clean.json",
        help="Path to enriched JIRA tickets JSON file"
    )

    parser.add_argument(
        "--output-json",
        type=Path,
        default="similarity_matrix_output.json",
        help="Output path for JSON similarity matrix"
    )

    parser.add_argument(
        "--output-csv",
        type=Path,
        default="similarity_matrix_output.csv",
        help="Output path for CSV similarity matrix"
    )

    parser.add_argument(
        "--model-preset",
        choices=PRESETS.keys(),
        default="qwen3",
        help="MLX model preset to use"
    )

    parser.add_argument(
        "--include-files",
        action="store_true",
        default=True,
        help="Include file changes in similarity calculation"
    )

    parser.add_argument(
        "--no-include-files",
        dest="include_files",
        action="store_false",
        help="Exclude file changes from similarity calculation"
    )

    parser.add_argument(
        "--temperature",
        type=float,
        default=0.1,
        help="LLM sampling temperature (lower = more consistent)"
    )

    parser.add_argument(
        "--max-retries",
        type=int,
        default=2,
        help="Maximum retries for failed LLM calls"
    )

    parser.add_argument(
        "--cache-file",
        type=Path,
        help="Path to cache file for storing similarity scores"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    args = parser.parse_args()

    # Validate input file exists
    if not args.tickets.exists():
        parser.error(f"Tickets file not found: {args.tickets}")

    return SimilarityConfig(
        tickets_file=args.tickets,
        output_json=args.output_json,
        output_csv=args.output_csv,
        model_preset=args.model_preset,
        include_files=args.include_files,
        temperature=args.temperature,
        max_retries=args.max_retries,
        cache_file=args.cache_file
    )


def main():
    """Main entry point."""
    # Parse arguments
    config = parse_arguments()

    # Set up logging
    log_level = getattr(logging, config.__dict__.get('log_level', 'INFO'))
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    logger = logging.getLogger(__name__)

    # Check MLX availability
    if not MLX_AVAILABLE:
        logger.error("MLX not available - please install mlx-lm package")
        sys.exit(1)

    try:
        # Create and run similarity matrix generator
        generator = SimilarityMatrixGenerator(config)
        success = generator.run()

        if success:
            logger.info("Similarity matrix generation completed successfully!")
            sys.exit(0)
        else:
            logger.error("Similarity matrix generation completed with validation errors")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Process failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
